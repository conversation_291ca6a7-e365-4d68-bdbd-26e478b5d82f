import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import {
  DollarSign,
  TrendingUp,
  Calendar,
  Users,
  Building2,
  Clock,
  Star,
  Activity
} from 'lucide-react';
import { AnalyticsService } from '@/services/analyticsService';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/sonner';
import { InlineLoader } from '@/components/ui/loading-spinner';

const Analytics = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalRevenue: 0,
    monthlyGrowth: 0,
    totalBookings: 0,
    bookingGrowth: 0,
    totalSalons: 0,
    salonGrowth: 0,
    avgRating: 0,
    ratingGrowth: 0,
  });
  const [revenueData, setRevenueData] = useState<Array<{month: string, revenue: number}>>([]);
  const [popularServices, setPopularServices] = useState<Array<{name: string, bookings: number, revenue: number}>>([]);
  const [salonPerformance, setSalonPerformance] = useState<Array<{name: string, revenue: number, bookings: number}>>([]);

  // Load analytics data from Firebase
  useEffect(() => {
    const loadAnalyticsData = async () => {
      if (!user || user.role !== 'admin') return;

      try {
        setLoading(true);

        // Load dashboard stats and analytics data
        const [dashboardStats, revenueAnalytics, popularServicesData, salonPerformanceData, peakHoursData] = await Promise.all([
          AnalyticsService.getAdminDashboardStats(),
          AnalyticsService.getRevenueAnalytics(),
          AnalyticsService.getPopularServices(),
          AnalyticsService.getSalonPerformance(),
          AnalyticsService.getPeakHours()
        ]);

        // Set stats with real data
        setStats({
          totalRevenue: dashboardStats.totalRevenue,
          monthlyGrowth: 12.5, // This would need to be calculated from historical data
          totalBookings: dashboardStats.totalBookings,
          bookingGrowth: 8.3, // This would need to be calculated from historical data
          totalSalons: dashboardStats.totalSalons,
          salonGrowth: 15.2, // This would need to be calculated from historical data
          avgRating: 4.7, // This would need to be calculated from booking ratings
          ratingGrowth: 2.1, // This would need to be calculated from historical data
        });

        // Set real data for charts
        setRevenueData(revenueAnalytics.revenueByMonth);

        // Transform popular services data to match expected format
        const transformedServices = popularServicesData.map(service => ({
          name: service.serviceName,
          bookings: service.bookings,
          revenue: service.bookings * 100 // Estimate revenue based on bookings
        }));
        setPopularServices(transformedServices);

        // Set salon performance data
        setSalonPerformance(salonPerformanceData.map(salon => ({
          name: salon.salonName,
          revenue: salon.revenue,
          bookings: salon.bookings
        })));

        // Set peak hours data
        setPeakHours(peakHoursData);

      } catch (error) {
        console.error('Error loading analytics data:', error);
        toast.error('Failed to load analytics data');
      } finally {
        setLoading(false);
      }
    };

    loadAnalyticsData();
  }, [user]);

  const [peakHours, setPeakHours] = useState<Array<{hour: string, bookings: number}>>([]);

  const bookingStatusData = [
    { name: 'Completed', value: 68, color: '#10B981' },
    { name: 'Confirmed', value: 22, color: '#3B82F6' },
    { name: 'Pending', value: 8, color: '#F59E0B' },
    { name: 'Cancelled', value: 2, color: '#EF4444' },
  ];

  if (loading) {
    return <InlineLoader text="Loading analytics..." height="h-96" />;
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-glamspot-neutral-900">Analytics Dashboard</h1>
        <p className="text-glamspot-neutral-600 mt-2">
          Comprehensive insights into your platform's performance
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Total Revenue
            </CardTitle>
            <DollarSign className="h-4 w-4 text-glamspot-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">
              Tsh {stats.totalRevenue.toLocaleString()}
            </div>
            <p className="text-xs text-green-600 flex items-center mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              +{stats.monthlyGrowth}% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Total Bookings
            </CardTitle>
            <Calendar className="h-4 w-4 text-glamspot-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{stats.totalBookings}</div>
            <p className="text-xs text-green-600 flex items-center mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              +{stats.bookingGrowth}% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Active Salons
            </CardTitle>
            <Building2 className="h-4 w-4 text-glamspot-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{stats.totalSalons}</div>
            <p className="text-xs text-green-600 flex items-center mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              +{stats.salonGrowth}% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Average Rating
            </CardTitle>
            <Star className="h-4 w-4 text-yellow-400 fill-current" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{stats.avgRating}</div>
            <p className="text-xs text-green-600 flex items-center mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              +{stats.ratingGrowth}% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Trend */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5 text-glamspot-primary" />
              Revenue Trend
            </CardTitle>
            <CardDescription>Monthly revenue over the last 6 months</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={revenueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [`Tsh ${value}`, 'Revenue']} />
                <Line 
                  type="monotone" 
                  dataKey="revenue" 
                  stroke="hsl(var(--glamspot-primary))" 
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Booking Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5 text-glamspot-primary" />
              Booking Status Distribution
            </CardTitle>
            <CardDescription>Current booking status breakdown</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={bookingStatusData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}%`}
                >
                  {bookingStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Additional Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Peak Hours */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-glamspot-primary" />
              Peak Booking Hours
            </CardTitle>
            <CardDescription>Most popular booking times throughout the day</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={peakHours}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="bookings" fill="hsl(var(--glamspot-primary))" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Top Performing Salons */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="w-5 h-5 text-glamspot-primary" />
              Top Performing Salons
            </CardTitle>
            <CardDescription>Salons ranked by revenue performance</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={salonPerformance} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="name" type="category" width={100} />
                <Tooltip formatter={(value) => [`Tsh ${value}`, 'Revenue']} />
                <Bar dataKey="revenue" fill="hsl(var(--glamspot-primary))" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Popular Services Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="w-5 h-5 text-glamspot-primary" />
            Popular Services
          </CardTitle>
          <CardDescription>Most booked services and their revenue contribution</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {popularServices.map((service, index) => (
              <div key={service.name} className="flex items-center justify-between p-4 bg-glamspot-neutral-50 rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="w-8 h-8 bg-glamspot-primary rounded-full flex items-center justify-center text-white font-bold text-sm">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium text-glamspot-neutral-900">{service.name}</p>
                    <p className="text-sm text-glamspot-neutral-500">{service.bookings} bookings</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-glamspot-neutral-900">Tsh {service.revenue.toLocaleString()}</p>
                  <Badge variant="outline" className="mt-1">
                    Tsh {Math.round(service.revenue / service.bookings)} avg
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Analytics;
