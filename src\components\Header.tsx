import { useState } from "react";
import { Link } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import { SearchBar } from "./SearchBar";
import { Globe, Menu, User, LogOut, Settings, LayoutDashboard, Plus, Search } from "lucide-react";

export const Header = () => {
  const { user, isAuthenticated, isAdmin, isSalonOwner, logout } = useAuth();
  const [isMobileSearchOpen, setIsMobileSearchOpen] = useState(false);

  const handleLogout = () => {
    logout();
  };

  return (
    <header className="sticky top-0 z-50 bg-white border-b border-glamspot-neutral-200 shadow-sm">
      <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8">
        <div className="flex items-center justify-between py-2 sm:py-4 gap-1 sm:gap-2 lg:gap-4">
          {/* Logo */}
          <Link to="/" className="flex items-center gap-1 sm:gap-2 text-glamspot-primary hover:text-glamspot-primary-dark transition-colors flex-shrink-0">
            <div className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center">
              <img src="/logo.svg" alt="GlamSpot" className="w-6 h-6 sm:w-8 sm:h-8" />
            </div>
            <h1 className="text-sm sm:text-lg lg:text-2xl font-bold tracking-tight hidden xs:block">GlamSpot</h1>
          </Link>

          {/* Search Bar - Hidden on very small screens, shown on sm+ */}
          <div className="hidden sm:flex flex-1 max-w-xl lg:max-w-2xl mx-2 lg:mx-4">
            <SearchBar />
          </div>

          {/* Right Navigation */}
          <div className="flex items-center gap-1 sm:gap-2 lg:gap-4 flex-shrink-0">
            {/* Mobile Search Button - only visible on small screens */}
            <Dialog open={isMobileSearchOpen} onOpenChange={setIsMobileSearchOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="icon" className="sm:hidden text-glamspot-neutral-700 hover:text-glamspot-primary w-8 h-8">
                  <Search className="w-4 h-4" />
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Search Salons</DialogTitle>
                </DialogHeader>
                <div className="mt-4">
                  <SearchBar />
                </div>
              </DialogContent>
            </Dialog>

            {/* List your space button - responsive */}
            <Button variant="ghost" className="hidden md:flex text-glamspot-neutral-700 hover:text-glamspot-primary text-xs lg:text-sm px-2 lg:px-4" asChild>
              <Link to="/register-salon">
                <Plus className="w-3 h-3 lg:w-4 lg:h-4 mr-1 lg:mr-2" />
                <span className="hidden lg:inline">List your space</span>
                <span className="lg:hidden">List</span>
              </Link>
            </Button>

            {/* Globe button - hidden on very small screens */}
            <Button variant="ghost" size="icon" className="hidden sm:flex text-glamspot-neutral-700 hover:text-glamspot-primary w-8 h-8 lg:w-10 lg:h-10">
              <Globe className="w-4 h-4 lg:w-5 lg:h-5" />
            </Button>

            {isAuthenticated ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center gap-1 sm:gap-2 border border-glamspot-neutral-200 rounded-full p-1 sm:p-2 hover:shadow-md transition-shadow">
                    <Menu className="w-3 h-3 sm:w-4 sm:h-4 text-glamspot-neutral-700" />
                    <div className="w-6 h-6 sm:w-8 sm:h-8 bg-glamspot-primary rounded-full flex items-center justify-center">
                      <span className="text-white text-xs sm:text-sm font-medium">
                        {user?.name?.charAt(0) || 'U'}
                      </span>
                    </div>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium">{user?.name}</p>
                      <p className="text-xs text-glamspot-neutral-500">{user?.email}</p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />

                  {isAdmin && (
                    <DropdownMenuItem asChild>
                      <Link to="/admin" className="flex items-center">
                        <LayoutDashboard className="w-4 h-4 mr-2" />
                        Admin Dashboard
                      </Link>
                    </DropdownMenuItem>
                  )}

                  {isSalonOwner && (
                    <DropdownMenuItem asChild>
                      <Link to="/salon-owner" className="flex items-center">
                        <Settings className="w-4 h-4 mr-2" />
                        Salon Dashboard
                      </Link>
                    </DropdownMenuItem>
                  )}

                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout} className="text-red-600">
                    <LogOut className="w-4 h-4 mr-2" />
                    Sign out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center gap-1 sm:gap-2">
                <Button variant="ghost" className="text-xs sm:text-sm px-2 sm:px-4" asChild>
                  <Link to="/login">Sign in</Link>
                </Button>
                <div className="flex items-center gap-1 sm:gap-2 border border-glamspot-neutral-200 rounded-full p-1 sm:p-2 hover:shadow-md transition-shadow">
                  <Menu className="w-3 h-3 sm:w-4 sm:h-4 text-glamspot-neutral-700" />
                  <div className="w-6 h-6 sm:w-8 sm:h-8 bg-glamspot-neutral-500 rounded-full flex items-center justify-center">
                    <User className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};