import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { FullPageLoader } from '@/components/ui/loading-spinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
  requireSalonOwner?: boolean;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAdmin = false,
  requireSalonOwner = false,
}) => {
  const { isAuthenticated, isAdmin, isSalonOwner, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return <FullPageLoader text="Loading..." />;
  }

  if (!isAuthenticated) {
    // Redirect to login with return url
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (requireAdmin && !isAdmin) {
    return <Navigate to="/login" state={{ from: location, error: 'Admin access required' }} replace />;
  }

  if (requireSalonOwner && !isSalonOwner) {
    return <Navigate to="/login" state={{ from: location, error: 'Salon owner access required' }} replace />;
  }

  return <>{children}</>;
};
