import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye, 
  Users,
  Mail,
  Phone,
  Building2,
  User
} from 'lucide-react';
import { Staff, Salon } from '@/types';
import { StaffService } from '@/services/staffService';
import { SalonService } from '@/services/salonService';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/sonner';
import { useEffect } from 'react';

const StaffManagement = () => {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [salonFilter, setSalonFilter] = useState<string>('all');
  const [selectedStaff, setSelectedStaff] = useState<Staff | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingStaff, setEditingStaff] = useState<Staff | null>(null);
  const [staff, setStaff] = useState<Staff[]>([]);
  const [salons, setSalons] = useState<Salon[]>([]);
  const [loading, setLoading] = useState(true);

  // Form state for add/edit
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    specialty: '',
    bio: '',
    salonId: '',
  });

  // Load staff and salons from Firebase
  useEffect(() => {
    const loadData = async () => {
      if (!user || user.role !== 'admin') return;

      try {
        setLoading(true);
        const [staffData, salonsData] = await Promise.all([
          StaffService.getAllStaff(),
          SalonService.getAllSalons()
        ]);

        setStaff(staffData);
        setSalons(salonsData);
      } catch (error) {
        console.error('Error loading data:', error);
        toast.error('Failed to load staff and salons');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [user]);

  // Create salon lookup
  const salonLookup = salons.reduce((acc, salon) => {
    acc[salon.id] = salon.name;
    return acc;
  }, {} as Record<string, string>);

  const filteredStaff = staff.filter(member => {
    const matchesSearch = 
      member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.specialty.toLowerCase().includes(searchTerm.toLowerCase()) ||
      salonLookup[member.salonId]?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesSalon = salonFilter === 'all' || member.salonId === salonFilter;
    
    return matchesSearch && matchesSalon;
  });

  const handleViewStaff = (staff: Staff) => {
    setSelectedStaff(staff);
  };

  const handleEditStaff = (staff: Staff) => {
    setEditingStaff(staff);
    setFormData({
      name: staff.name,
      email: staff.email || '',
      phone: staff.phone || '',
      specialty: staff.specialty,
      bio: staff.bio || '',
      salonId: staff.salonId,
    });
    setIsEditDialogOpen(true);
  };

  const handleDeleteStaff = async (staff: Staff) => {
    try {
      await StaffService.deleteStaff(staff.id);
      toast.success(`Staff member "${staff.name}" has been deleted`);

      // Reload staff data
      const staffData = await StaffService.getAllStaff();
      setStaff(staffData);
    } catch (error) {
      console.error('Error deleting staff member:', error);
      toast.error('Failed to delete staff member. Please try again.');
    }
  };

  const handleAddStaff = () => {
    setEditingStaff(null);
    setFormData({
      name: '',
      email: '',
      phone: '',
      specialty: '',
      bio: '',
      salonId: '',
    });
    setIsAddDialogOpen(true);
  };

  const handleCloseAddDialog = (open: boolean) => {
    setIsAddDialogOpen(open);
    if (!open) {
      setFormData({
        name: '',
        email: '',
        phone: '',
        specialty: '',
        bio: '',
        salonId: '',
      });
    }
  };

  const handleCloseEditDialog = (open: boolean) => {
    setIsEditDialogOpen(open);
    if (!open) {
      setEditingStaff(null);
      setFormData({
        name: '',
        email: '',
        phone: '',
        specialty: '',
        bio: '',
        salonId: '',
      });
    }
  };

  const handleSubmitForm = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.specialty || !formData.salonId) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      if (editingStaff) {
        // Update existing staff member
        await StaffService.updateStaff(editingStaff.id, {
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
          specialty: formData.specialty,
          bio: formData.bio,
          salonId: formData.salonId
        });
        toast.success(`Staff member "${formData.name}" has been updated`);
        setIsEditDialogOpen(false);
        setEditingStaff(null);
      } else {
        // Create new staff member
        await StaffService.createStaff({
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
          specialty: formData.specialty,
          bio: formData.bio,
          salonId: formData.salonId
        });
        toast.success(`Staff member "${formData.name}" has been added`);
        setIsAddDialogOpen(false);
      }

      // Reload staff data
      const staffData = await StaffService.getAllStaff();
      setStaff(staffData);

    } catch (error) {
      console.error('Error saving staff member:', error);
      toast.error('Failed to save staff member. Please try again.');
    }

    setFormData({
      name: '',
      email: '',
      phone: '',
      specialty: '',
      bio: '',
      salonId: '',
    });
  };

  const renderStaffForm = () => (
    <form onSubmit={handleSubmitForm} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Full Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="Enter full name"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="salon">Salon *</Label>
          <Select value={formData.salonId} onValueChange={(value) => setFormData(prev => ({ ...prev, salonId: value }))}>
            <SelectTrigger>
              <SelectValue placeholder="Select salon" />
            </SelectTrigger>
            <SelectContent>
              {salons.map(salon => (
                <SelectItem key={salon.id} value={salon.id}>{salon.name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
            placeholder="Enter email address"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="phone">Phone</Label>
          <Input
            id="phone"
            type="tel"
            value={formData.phone}
            onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
            placeholder="Enter phone number"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="specialty">Specialty *</Label>
        <Input
          id="specialty"
          value={formData.specialty}
          onChange={(e) => setFormData(prev => ({ ...prev, specialty: e.target.value }))}
          placeholder="e.g., Hair cutting and styling specialist"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="bio">Bio</Label>
        <Textarea
          id="bio"
          value={formData.bio}
          onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
          placeholder="Enter staff member bio"
          className="min-h-[80px]"
        />
      </div>
      
      <div className="flex justify-end gap-3">
        <Button 
          type="button" 
          variant="outline" 
          onClick={() => {
            setIsAddDialogOpen(false);
            setIsEditDialogOpen(false);
            setEditingStaff(null);
          }}
        >
          Cancel
        </Button>
        <Button type="submit" className="bg-glamspot-primary hover:bg-glamspot-primary-dark">
          {editingStaff ? 'Update Staff' : 'Add Staff'}
        </Button>
      </div>
    </form>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-glamspot-primary mx-auto"></div>
          <p className="mt-2 text-glamspot-neutral-600">Loading staff...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-glamspot-neutral-900">Staff Management</h1>
          <p className="text-glamspot-neutral-600 mt-2">
            Manage stylists, schedules, and specialties across all salons
          </p>
        </div>
        <Button onClick={handleAddStaff} className="bg-glamspot-primary hover:bg-glamspot-primary-dark">
          <Plus className="w-4 h-4 mr-2" />
          Add Staff
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-glamspot-neutral-400 w-4 h-4" />
              <Input
                placeholder="Search staff by name, email, specialty, or salon..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={salonFilter} onValueChange={setSalonFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by salon" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Salons</SelectItem>
                {salons.map(salon => (
                  <SelectItem key={salon.id} value={salon.id}>{salon.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Badge variant="outline" className="text-glamspot-neutral-600">
              {filteredStaff.length} staff member{filteredStaff.length !== 1 ? 's' : ''}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Staff Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5 text-glamspot-primary" />
            All Staff
          </CardTitle>
          <CardDescription>
            Complete list of all staff members across all salons
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Staff Member</TableHead>
                <TableHead>Salon</TableHead>
                <TableHead>Specialty</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredStaff.map((member) => (
                <TableRow key={member.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-glamspot-neutral-200 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-glamspot-neutral-600" />
                      </div>
                      <div>
                        <p className="font-medium text-glamspot-neutral-900">{member.name}</p>
                        <p className="text-sm text-glamspot-neutral-500 truncate max-w-xs">
                          {member.bio}
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Building2 className="w-4 h-4 text-glamspot-neutral-500" />
                      <span className="text-sm">{salonLookup[member.salonId]}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <p className="text-sm text-glamspot-neutral-900">{member.specialty}</p>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {member.email && (
                        <div className="flex items-center gap-1 text-sm text-glamspot-neutral-600">
                          <Mail className="w-3 h-3" />
                          {member.email}
                        </div>
                      )}
                      {member.phone && (
                        <div className="flex items-center gap-1 text-sm text-glamspot-neutral-600">
                          <Phone className="w-3 h-3" />
                          {member.phone}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={member.isActive ? 'default' : 'secondary'}
                      className={member.isActive ? 'bg-green-100 text-green-800' : ''}
                    >
                      {member.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewStaff(member)}>
                          <Eye className="w-4 h-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditStaff(member)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit Staff
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDeleteStaff(member)}
                          className="text-red-600"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete Staff
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add Staff Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={handleCloseAddDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New Staff Member</DialogTitle>
            <DialogDescription>
              Add a new staff member to a salon
            </DialogDescription>
          </DialogHeader>
          {renderStaffForm()}
        </DialogContent>
      </Dialog>

      {/* Edit Staff Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={handleCloseEditDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Staff Member</DialogTitle>
            <DialogDescription>
              Update staff member information
            </DialogDescription>
          </DialogHeader>
          {renderStaffForm()}
        </DialogContent>
      </Dialog>

      {/* Staff Details Dialog */}
      <Dialog open={!!selectedStaff} onOpenChange={() => setSelectedStaff(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{selectedStaff?.name}</DialogTitle>
            <DialogDescription>Staff member details and information</DialogDescription>
          </DialogHeader>
          {selectedStaff && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Salon</label>
                  <p className="text-glamspot-neutral-900">{salonLookup[selectedStaff.salonId]}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Specialty</label>
                  <p className="text-glamspot-neutral-900">{selectedStaff.specialty}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Email</label>
                  <p className="text-glamspot-neutral-900">{selectedStaff.email || 'Not provided'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Phone</label>
                  <p className="text-glamspot-neutral-900">{selectedStaff.phone || 'Not provided'}</p>
                </div>
              </div>
              {selectedStaff.bio && (
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Bio</label>
                  <p className="text-glamspot-neutral-900">{selectedStaff.bio}</p>
                </div>
              )}
              <div>
                <label className="text-sm font-medium text-glamspot-neutral-700">Status</label>
                <Badge
                  variant={selectedStaff.isActive ? 'default' : 'secondary'}
                  className={selectedStaff.isActive ? 'bg-green-100 text-green-800 ml-2' : 'ml-2'}
                >
                  {selectedStaff.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StaffManagement;
