import { useEffect, useState } from "react";
import { Header } from "@/components/Header";
import { StudioCard } from "@/components/StudioCard";
import { FilterControls } from "@/components/FilterControls";
import { FilterModal } from "@/components/FilterModal";
import { MapView } from "@/components/MapViewNew";
import { useSearchFilter } from "@/contexts/SearchFilterContext";
import { SalonService } from "@/services/salonService";
import { FullPageLoader } from "@/components/ui/loading-spinner";
import { toast } from "sonner";



const Index = () => {
  const {
    filteredSalons,
    setAllSalons,
    viewMode
  } = useSearchFilter();

  const [loading, setLoading] = useState(true);

  // Load salon data from Firebase
  useEffect(() => {
    const loadSalons = async () => {
      try {
        setLoading(true);
        const salonsData = await SalonService.getActiveSalons();
        setAllSalons(salonsData);
      } catch (error) {
        console.error('Error loading salons:', error);
        toast.error('Failed to load salons. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    loadSalons();

    // Set up real-time listener for salon changes
    const unsubscribe = SalonService.onSalonsChange((salons) => {
      setAllSalons(salons);
    });

    return () => unsubscribe();
  }, [setAllSalons]);

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <main className="max-w-7xl mx-auto px-6 lg:px-8 py-8">
          <FullPageLoader text="Loading salons..." className="min-h-[50vh] bg-white" />
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 xl:px-8 py-3 sm:py-6 lg:py-8">
        <div className="flex flex-col space-y-3 sm:space-y-0 sm:flex-row sm:items-center justify-between mb-4 sm:mb-6 lg:mb-8">
          <h1 className="text-lg sm:text-xl lg:text-2xl xl:text-3xl font-bold text-glamspot-neutral-900 leading-tight">
            {filteredSalons.length > 0
              ? `${filteredSalons.length} place${filteredSalons.length !== 1 ? 's' : ''} in Dodoma`
              : 'No places found'
            }
          </h1>
          <div className="flex justify-end">
            <FilterControls />
          </div>
        </div>

        {viewMode === 'grid' ? (
          <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-3 sm:gap-4 lg:gap-6">
            {filteredSalons.map((salon) => (
              <StudioCard
                key={salon.id}
                id={salon.id}
                name={salon.name}
                location={salon.location}
                rating={salon.rating}
                image={salon.images[0]}
              />
            ))}
          </div>
        ) : (
          <div className="w-full h-[60vh] sm:h-[70vh] lg:h-[80vh] rounded-lg overflow-hidden">
            <MapView />
          </div>
        )}

        {filteredSalons.length === 0 && viewMode === 'grid' && (
          <div className="text-center py-8 sm:py-12 px-4">
            <h3 className="text-base sm:text-lg font-medium text-glamspot-neutral-900 mb-2">
              No salons match your criteria
            </h3>
            <p className="text-sm sm:text-base text-glamspot-neutral-600 mb-4 max-w-md mx-auto">
              Try adjusting your search or filters to find more options.
            </p>
          </div>
        )}
      </main>

      <FilterModal />
    </div>
  );
};

export default Index;
