import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { Star, ChevronRight, Edit } from "lucide-react";
import { Header } from "@/components/Header";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { SalonService } from '@/services/salonService';
import { ServiceService } from '@/services/serviceService';
import { StaffService } from '@/services/staffService';
import { Salon, Service, Staff } from '@/types';
import { InlineLoader } from '@/components/ui/loading-spinner';
import { LeafletMap } from '@/components/LeafletMap';
import { ReviewForm } from '@/components/ReviewForm';
import { ReviewList } from '@/components/ReviewList';
import { ReviewService } from '@/services/reviewService';
import { toast } from 'sonner';
import salon1 from "@/assets/salon-1.jpg";
import salon2 from "@/assets/salon-2.jpg";
import salon3 from "@/assets/salon-3.jpg";
import salon4 from "@/assets/salon-4.jpg";
import salon5 from "@/assets/salon-5.jpg";
import salon6 from "@/assets/salon-6.jpg";

const salons = [
  {
    id: "1",
    name: "The Hair Lounge",
    location: "San Francisco",
    distance: "Downtown",
    rating: 4.8,
    reviews: 120,
    price: 50,
    description: "Welcome to The Hair Lounge, a premier salon in the heart of San Francisco. Our expert stylists offer a range of services from haircuts and coloring to styling and treatments. We use only the best products to ensure your hair looks and feels its best. Book your appointment today and experience the difference!",
    images: [salon1, salon2, salon3, salon4, salon5],
    services: [
      { name: "Haircut", price: 50 },
      { name: "Hair Coloring", price: 120 },
      { name: "Styling", price: 80 },
      { name: "Treatment", price: 60 }
    ],
    stylists: [
      { name: "Emily Carter", role: "Expert Stylist", image: salon1 },
      { name: "Sophia Bennett", role: "Color Specialist", image: salon2 },
      { name: "Olivia Hayes", role: "Styling Pro", image: salon3 }
    ],
    ratings: {
      cleanliness: 4.9,
      accuracy: 4.8,
      communication: 4.9,
      location: 5.0,
      value: 4.7
    }
  },
  {
    id: "2",
    name: "Urban Beauty Lounge",
    location: "Mission District",
    distance: "5 miles away",
    rating: 4.9,
    reviews: 89,
    price: 65,
    description: "Urban Beauty Lounge brings modern styling to the Mission District. Our team of experienced professionals specializes in contemporary cuts and vibrant color treatments.",
    images: [salon2, salon3, salon4, salon5, salon6],
    services: [
      { name: "Haircut", price: 65 },
      { name: "Hair Coloring", price: 130 },
      { name: "Styling", price: 85 },
      { name: "Treatment", price: 70 }
    ],
    stylists: [
      { name: "Marcus Johnson", role: "Senior Stylist", image: salon2 },
      { name: "Luna Rodriguez", role: "Color Expert", image: salon3 },
      { name: "Alex Chen", role: "Style Director", image: salon4 }
    ],
    ratings: {
      cleanliness: 4.8,
      accuracy: 4.9,
      communication: 4.8,
      location: 4.9,
      value: 4.8
    }
  }
];

const SalonDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [salon, setSalon] = useState<Salon | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [staff, setStaff] = useState<Staff[]>([]);
  const [loading, setLoading] = useState(true);
  const [salonRating, setSalonRating] = useState({ averageRating: 0, totalReviews: 0 });

  // Fallback to mock data for display
  const mockSalon = salons.find(s => s.id === id) || salons[0];

  useEffect(() => {
    const loadSalonData = async () => {
      if (!id) return;

      try {
        setLoading(true);

        // Load salon data, services, and staff
        const [salonData, servicesData, staffData] = await Promise.all([
          SalonService.getSalonById(id),
          ServiceService.getServicesBySalon(id),
          StaffService.getStaffBySalon(id)
        ]);

        if (salonData) {
          setSalon(salonData);
          setServices(servicesData);
          setStaff(staffData);

          // Load salon rating from reviews
          const rating = await ReviewService.calculateSalonRating(id);
          setSalonRating(rating);
        } else {
          toast.error('Salon not found');
        }
      } catch (error) {
        console.error('Error loading salon data:', error);
        toast.error('Failed to load salon data');
      } finally {
        setLoading(false);
      }
    };

    loadSalonData();
  }, [id]);

  const handleBookNow = () => {
    navigate(`/salon/${id}/booking`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-glamspot-neutral-50">
        <Header />
        <div className="flex justify-center items-center py-20">
          <InlineLoader text="Loading salon details..." />
        </div>
      </div>
    );
  }

  // Use Firebase data if available, otherwise fallback to mock data
  const displaySalon = salon || mockSalon;
  const displayServices = services.length > 0 ? services : mockSalon.services;
  const displayStaff = staff.length > 0 ? staff : mockSalon.stylists;

  return (
    <div className="min-h-screen bg-glamspot-neutral-50">
      <Header />
      
      <main className="px-3 sm:px-4 lg:px-6 xl:px-8 2xl:px-40 flex flex-1 justify-center py-4 sm:py-6 lg:py-10">
        <div className="flex flex-col max-w-[1200px] flex-1 gap-4 sm:gap-6 lg:gap-8">
          {/* Breadcrumb */}
          <div className="flex items-center gap-2 text-xs sm:text-sm text-glamspot-neutral-500">
            <Link to="/" className="hover:text-glamspot-primary hover:underline">
              Home
            </Link>
            <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4" />
            <span className="font-medium text-glamspot-neutral-800">Salon Details</span>
          </div>

          {/* Title and Rating */}
          <div className="flex flex-col gap-3 sm:gap-4">
            <div className="flex flex-col gap-2">
              <h1 className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold text-glamspot-neutral-900 leading-tight">{displaySalon.name}</h1>
              <div className="flex flex-wrap items-center gap-1.5 sm:gap-2 text-xs sm:text-sm lg:text-base text-glamspot-neutral-600">
                <Star className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 text-yellow-500 fill-current" />
                <span className="font-semibold">{salonRating.averageRating || 'No rating'}</span>
                <span className="text-glamspot-neutral-400">·</span>
                <a href="#reviews" className="underline hover:text-glamspot-primary">
                  {salonRating.totalReviews} review{salonRating.totalReviews !== 1 ? 's' : ''}
                </a>
                <span className="text-glamspot-neutral-400 hidden sm:inline">·</span>
                <span className="w-full sm:w-auto">{displaySalon.location}</span>
              </div>
            </div>
          </div>

          {/* Image Gallery */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-1 sm:gap-2 h-40 sm:h-48 md:h-64 lg:h-80 xl:h-96 overflow-hidden rounded-lg sm:rounded-xl lg:rounded-2xl">
            <div className="col-span-2 row-span-2">
              <img
                className="w-full h-full object-cover"
                src={displaySalon.images[0]}
                alt={displaySalon.name}
              />
            </div>
            {displaySalon.images.slice(1, 5).map((image, index) => (
              <img
                key={index}
                className="w-full h-full object-cover hidden sm:block"
                src={image}
                alt={`${displaySalon.name} interior ${index + 2}`}
              />
            ))}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-12 xl:gap-16">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-4 sm:space-y-6 lg:space-y-8">
              {/* About Section */}
              <section>
                <h2 className="text-lg sm:text-xl lg:text-2xl font-bold mb-3 sm:mb-4 text-glamspot-neutral-900">About {displaySalon.name}</h2>
                <p className="text-sm sm:text-base text-glamspot-neutral-600 leading-relaxed">{displaySalon.description}</p>
              </section>

              <div className="border-t border-glamspot-neutral-200"></div>

              {/* Services Section */}
              <section>
                <h2 className="text-lg sm:text-xl lg:text-2xl font-bold mb-3 sm:mb-4 text-glamspot-neutral-900">Services & Pricing</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  {displayServices.map((service, index) => (
                    <div key={'id' in service ? String(service.id) : index} className="flex items-center justify-between p-3 sm:p-4 border border-glamspot-neutral-200 rounded-lg">
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-glamspot-neutral-900 text-sm sm:text-base truncate">{service.name}</h3>
                        {'description' in service && service.description && (
                          <p className="text-xs sm:text-sm text-glamspot-neutral-600 mt-1 line-clamp-2">{String(service.description)}</p>
                        )}
                        {'duration' in service && service.duration && (
                          <p className="text-xs text-glamspot-neutral-500 mt-1">{String(service.duration)} min</p>
                        )}
                      </div>
                      <span className="font-semibold text-glamspot-primary text-sm sm:text-base ml-2 flex-shrink-0">Tsh {service.price}</span>
                    </div>
                  ))}
                </div>
              </section>

              <div className="border-t border-glamspot-neutral-200"></div>

              {/* Stylists Section */}
              <section>
                <h2 className="text-xl sm:text-2xl font-bold mb-4 text-glamspot-neutral-900">Stylists</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
                  {displayStaff.map((member: any, index: number) => (
                    <div key={member.id || index} className="text-center p-3 sm:p-4 border border-glamspot-neutral-200 rounded-lg">
                      <div className="w-16 h-16 sm:w-20 sm:h-20 bg-glamspot-primary/10 rounded-full mx-auto mb-3 flex items-center justify-center">
                        <Star className="w-6 h-6 sm:w-8 sm:h-8 text-glamspot-primary" />
                      </div>
                      <p className="font-semibold text-glamspot-neutral-900 text-sm sm:text-base">{member.name}</p>
                      <p className="text-xs sm:text-sm text-glamspot-neutral-500">{member.specialty || member.role}</p>
                      {member.bio && (
                        <p className="text-xs text-glamspot-neutral-500 mt-2 line-clamp-2">{member.bio}</p>
                      )}
                    </div>
                  ))}
                </div>
              </section>

              <div className="border-t border-glamspot-neutral-200"></div>

              {/* Location Section */}
              <section>
                <h2 className="text-lg sm:text-xl lg:text-2xl font-bold mb-3 sm:mb-4 text-glamspot-neutral-900">Where you'll be</h2>
                <div className="w-full h-60 sm:h-72 lg:h-80 rounded-lg sm:rounded-xl lg:rounded-2xl overflow-hidden border border-glamspot-neutral-200">
                  <LeafletMap
                    salons={[{
                      id: displaySalon.id,
                      name: displaySalon.name,
                      location: displaySalon.location,
                      coordinates: ('coordinates' in displaySalon && displaySalon.coordinates) || { lat: -6.1630, lng: 35.7516 },
                      rating: displaySalon.rating
                    }]}
                    center={('coordinates' in displaySalon && displaySalon.coordinates) || { lat: -6.1630, lng: 35.7516 }}
                    zoom={15}
                    height="100%"
                  />
                </div>
                {'address' in displaySalon && displaySalon.address && (
                  <p className="mt-2 sm:mt-3 text-sm sm:text-base text-glamspot-neutral-600">{displaySalon.address}</p>
                )}
              </section>
            </div>

            {/* Booking Sidebar */}
            <div className="lg:col-span-1">
              <div className="lg:sticky lg:top-28 p-4 sm:p-6 rounded-lg sm:rounded-xl lg:rounded-2xl border border-glamspot-neutral-200 shadow-md lg:shadow-lg bg-white">
                <Button
                  onClick={handleBookNow}
                  className="w-full bg-glamspot-primary hover:bg-glamspot-primary-dark text-white font-bold py-2.5 sm:py-3 text-sm sm:text-base"
                >
                  Book Now
                </Button>
              </div>
            </div>
          </div>

          {/* Reviews Section */}
          <div className="border-t border-glamspot-neutral-200"></div>

          <section className="space-y-6 sm:space-y-8" id="reviews">
            <div className="flex items-center justify-between">
              <h2 className="text-lg sm:text-xl lg:text-2xl font-bold flex items-center gap-2 text-glamspot-neutral-900">
                <Star className="w-5 h-5 sm:w-6 sm:h-6 text-yellow-500 fill-current" />
                <span className="text-sm sm:text-base lg:text-xl">
                  {salonRating.averageRating || 'No rating'} · {salonRating.totalReviews} review{salonRating.totalReviews !== 1 ? 's' : ''}
                </span>
              </h2>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8">
              {/* Review Form */}
              <div className="lg:col-span-1 order-2 lg:order-1">
                <ReviewForm
                  salonId={displaySalon.id}
                  onReviewSubmitted={() => {
                    // Refresh rating after new review
                    ReviewService.calculateSalonRating(displaySalon.id).then(setSalonRating);
                  }}
                />
              </div>

              {/* Reviews List */}
              <div className="lg:col-span-2 order-1 lg:order-2">
                <ReviewList salonId={displaySalon.id} />
              </div>
            </div>
          </section>
        </div>
      </main>
    </div>
  );
};

export default SalonDetails;