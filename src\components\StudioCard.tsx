import { Star } from "lucide-react";
import { Card } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";

interface StudioCardProps {
  id: string;
  name: string;
  location: string;
  rating: number;
  image: string;
}

export const StudioCard = ({ id, name, location, rating, image }: StudioCardProps) => {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate(`/salon/${id}`);
  };

  return (
    <Card
      className="group cursor-pointer overflow-hidden border-0 shadow-sm hover:shadow-lg transition-all duration-300 w-full"
      onClick={handleClick}
    >
      <div className="aspect-[4/3] sm:aspect-[3/2] lg:aspect-[4/3] overflow-hidden rounded-t-lg bg-glamspot-neutral-100">
        <img
          src={image}
          alt={name}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />
      </div>

      <div className="p-2 xs:p-3 sm:p-4 space-y-1 sm:space-y-2">
        <div className="flex items-start justify-between gap-1 sm:gap-2">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-glamspot-neutral-900 truncate text-xs xs:text-sm sm:text-base leading-tight">
              {name}
            </h3>
            <p className="text-xs text-glamspot-neutral-500 truncate mt-0.5">
              {location}
            </p>
          </div>

          <div className="flex items-center gap-0.5 sm:gap-1 flex-shrink-0">
            <Star className="w-3 h-3 text-yellow-400 fill-current" />
            <span className="text-xs sm:text-sm font-medium text-glamspot-neutral-900">{rating}</span>
          </div>
        </div>
      </div>
    </Card>
  );
};