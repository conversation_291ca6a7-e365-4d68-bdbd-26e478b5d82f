import { FirestoreService } from './firestoreService';

export interface SecurityLog {
  id: string;
  event: 'failed_login' | 'rate_limit_exceeded' | 'invalid_input' | 'unauthorized_access' | 'suspicious_activity' | 'password_change';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  userEmail?: string;
  ipAddress?: string;
  userAgent?: string;
  details: Record<string, any>;
  timestamp: string;
  resolved: boolean;
  resolvedAt?: string;
  resolvedBy?: string;
}

export class SecurityLogService {
  private static readonly COLLECTION = 'security_logs';

  // Create a new security log entry
  static async createSecurityLog(
    event: SecurityLog['event'],
    severity: SecurityLog['severity'],
    details: Record<string, any> = {},
    metadata?: {
      userId?: string;
      userEmail?: string;
      ipAddress?: string;
      userAgent?: string;
    }
  ): Promise<string> {
    try {
      const securityLog: Omit<SecurityLog, 'id'> = {
        event,
        severity,
        userId: metadata?.userId,
        userEmail: metadata?.userEmail,
        ipAddress: metadata?.ipAddress || this.getClientIP(),
        userAgent: metadata?.userAgent || navigator.userAgent,
        details,
        timestamp: new Date().toISOString(),
        resolved: false
      };
      
      return await FirestoreService.create<SecurityLog>(this.COLLECTION, securityLog);
    } catch (error) {
      console.error('Error creating security log:', error);
      // Don't throw error here as security logging shouldn't break the main flow
      return '';
    }
  }

  // Get client IP address (basic implementation)
  private static getClientIP(): string {
    // In a real application, you would get this from the server
    // This is a placeholder for client-side logging
    return 'client-side';
  }

  // Log failed login attempt
  static async logFailedLogin(
    email: string,
    reason: string,
    metadata?: { ipAddress?: string; userAgent?: string }
  ): Promise<void> {
    await this.createSecurityLog(
      'failed_login',
      'medium',
      {
        email,
        reason,
        action: 'Failed login attempt'
      },
      {
        userEmail: email,
        ...metadata
      }
    );
  }

  // Log rate limit exceeded
  static async logRateLimitExceeded(
    identifier: string,
    action: string,
    metadata?: { userId?: string; userEmail?: string; ipAddress?: string }
  ): Promise<void> {
    await this.createSecurityLog(
      'rate_limit_exceeded',
      'high',
      {
        identifier,
        action,
        message: 'Rate limit exceeded for action'
      },
      metadata
    );
  }

  // Log invalid input detected
  static async logInvalidInput(
    field: string,
    value: string,
    reason: string,
    metadata?: { userId?: string; userEmail?: string }
  ): Promise<void> {
    await this.createSecurityLog(
      'invalid_input',
      'medium',
      {
        field,
        value: value.substring(0, 100), // Limit logged value length
        reason,
        action: 'Invalid input detected'
      },
      metadata
    );
  }

  // Log unauthorized access attempt
  static async logUnauthorizedAccess(
    resource: string,
    action: string,
    metadata?: { userId?: string; userEmail?: string; ipAddress?: string }
  ): Promise<void> {
    await this.createSecurityLog(
      'unauthorized_access',
      'high',
      {
        resource,
        action,
        message: 'Unauthorized access attempt'
      },
      metadata
    );
  }

  // Log suspicious activity
  static async logSuspiciousActivity(
    activity: string,
    details: Record<string, any>,
    metadata?: { userId?: string; userEmail?: string; ipAddress?: string }
  ): Promise<void> {
    await this.createSecurityLog(
      'suspicious_activity',
      'critical',
      {
        activity,
        ...details,
        message: 'Suspicious activity detected'
      },
      metadata
    );
  }

  // Log password change
  static async logPasswordChange(
    userId: string,
    metadata?: { userEmail?: string; ipAddress?: string }
  ): Promise<void> {
    await this.createSecurityLog(
      'password_change',
      'low',
      {
        action: 'Password changed successfully',
        message: 'User password was changed'
      },
      {
        userId,
        ...metadata
      }
    );
  }

  // Get security logs with filtering
  static async getSecurityLogs(
    filters?: {
      event?: SecurityLog['event'];
      severity?: SecurityLog['severity'];
      userId?: string;
      startDate?: string;
      endDate?: string;
      resolved?: boolean;
    },
    limit: number = 100
  ): Promise<SecurityLog[]> {
    try {
      const allLogs = await FirestoreService.getAll<SecurityLog>(this.COLLECTION);
      
      let filteredLogs = allLogs;

      if (filters) {
        if (filters.event) {
          filteredLogs = filteredLogs.filter(log => log.event === filters.event);
        }
        if (filters.severity) {
          filteredLogs = filteredLogs.filter(log => log.severity === filters.severity);
        }
        if (filters.userId) {
          filteredLogs = filteredLogs.filter(log => log.userId === filters.userId);
        }
        if (filters.startDate) {
          filteredLogs = filteredLogs.filter(log => log.timestamp >= filters.startDate!);
        }
        if (filters.endDate) {
          filteredLogs = filteredLogs.filter(log => log.timestamp <= filters.endDate!);
        }
        if (filters.resolved !== undefined) {
          filteredLogs = filteredLogs.filter(log => log.resolved === filters.resolved);
        }
      }

      return filteredLogs
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, limit);
    } catch (error) {
      console.error('Error getting security logs:', error);
      throw error;
    }
  }

  // Mark security log as resolved
  static async resolveSecurityLog(
    logId: string,
    resolvedBy: string
  ): Promise<void> {
    try {
      await FirestoreService.update<SecurityLog>(this.COLLECTION, logId, {
        resolved: true,
        resolvedAt: new Date().toISOString(),
        resolvedBy
      });
    } catch (error) {
      console.error('Error resolving security log:', error);
      throw error;
    }
  }

  // Get security statistics
  static async getSecurityStats(): Promise<{
    totalLogs: number;
    logsBySeverity: Record<SecurityLog['severity'], number>;
    logsByEvent: Record<SecurityLog['event'], number>;
    unresolvedCritical: number;
    recentActivity: number; // Last 24 hours
  }> {
    try {
      const allLogs = await this.getSecurityLogs({}, 1000);
      const twentyFourHoursAgo = new Date();
      twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);
      const cutoffTime = twentyFourHoursAgo.toISOString();

      const logsBySeverity: Record<SecurityLog['severity'], number> = {
        low: 0,
        medium: 0,
        high: 0,
        critical: 0
      };

      const logsByEvent: Record<SecurityLog['event'], number> = {
        failed_login: 0,
        rate_limit_exceeded: 0,
        invalid_input: 0,
        unauthorized_access: 0,
        suspicious_activity: 0,
        password_change: 0
      };

      let unresolvedCritical = 0;
      let recentActivity = 0;

      allLogs.forEach(log => {
        logsBySeverity[log.severity]++;
        logsByEvent[log.event]++;
        
        if (log.severity === 'critical' && !log.resolved) {
          unresolvedCritical++;
        }
        
        if (log.timestamp >= cutoffTime) {
          recentActivity++;
        }
      });

      return {
        totalLogs: allLogs.length,
        logsBySeverity,
        logsByEvent,
        unresolvedCritical,
        recentActivity
      };
    } catch (error) {
      console.error('Error getting security stats:', error);
      throw error;
    }
  }

  // Clean up old security logs (older than specified days)
  static async cleanupOldLogs(daysToKeep: number = 90): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      const cutoffDateString = cutoffDate.toISOString();

      const allLogs = await this.getSecurityLogs({}, 10000);
      const oldLogs = allLogs.filter(log => 
        log.timestamp < cutoffDateString && log.resolved
      );

      if (oldLogs.length > 0) {
        const batchOperations = oldLogs.map(log => ({
          type: 'delete' as const,
          collection: this.COLLECTION,
          id: log.id
        }));

        await FirestoreService.batchWrite(batchOperations);
        console.log(`Cleaned up ${oldLogs.length} old security logs`);
      }
    } catch (error) {
      console.error('Error cleaning up old security logs:', error);
      throw error;
    }
  }

  // Monitor for patterns that might indicate attacks
  static async detectSuspiciousPatterns(): Promise<{
    suspiciousIPs: string[];
    repeatedFailedLogins: Array<{ email: string; count: number }>;
    rateLimitViolations: Array<{ identifier: string; count: number }>;
  }> {
    try {
      const recentLogs = await this.getSecurityLogs({
        startDate: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // Last 24 hours
      }, 1000);

      // Track IPs with multiple security events
      const ipCounts: Record<string, number> = {};
      const emailFailures: Record<string, number> = {};
      const rateLimitViolations: Record<string, number> = {};

      recentLogs.forEach(log => {
        if (log.ipAddress && log.ipAddress !== 'client-side') {
          ipCounts[log.ipAddress] = (ipCounts[log.ipAddress] || 0) + 1;
        }

        if (log.event === 'failed_login' && log.userEmail) {
          emailFailures[log.userEmail] = (emailFailures[log.userEmail] || 0) + 1;
        }

        if (log.event === 'rate_limit_exceeded' && log.details.identifier) {
          const identifier = log.details.identifier;
          rateLimitViolations[identifier] = (rateLimitViolations[identifier] || 0) + 1;
        }
      });

      return {
        suspiciousIPs: Object.entries(ipCounts)
          .filter(([, count]) => count >= 10)
          .map(([ip]) => ip),
        repeatedFailedLogins: Object.entries(emailFailures)
          .filter(([, count]) => count >= 5)
          .map(([email, count]) => ({ email, count })),
        rateLimitViolations: Object.entries(rateLimitViolations)
          .filter(([, count]) => count >= 3)
          .map(([identifier, count]) => ({ identifier, count }))
      };
    } catch (error) {
      console.error('Error detecting suspicious patterns:', error);
      throw error;
    }
  }
}
