import { Button } from "@/components/ui/button";
import { useSearchFilter } from "@/contexts/SearchFilterContext";
import { SlidersHorizontal, Map, Grid3X3 } from "lucide-react";

export const FilterControls = () => {
  const {
    viewMode,
    setViewMode,
    isFilterModalOpen,
    setIsFilterModalOpen,
    filteredSalons
  } = useSearchFilter();

  const handleFilterClick = () => {
    setIsFilterModalOpen(true);
  };

  const handleMapToggle = () => {
    setViewMode(viewMode === 'grid' ? 'map' : 'grid');
  };

  return (
    <div className="flex items-center gap-2 sm:gap-4">
      <div className="text-xs sm:text-sm text-glamspot-neutral-600 hidden xs:block">
        {filteredSalons.length} {filteredSalons.length === 1 ? 'place' : 'places'}
      </div>

      <Button
        variant="outline"
        size="sm"
        className="flex items-center gap-1 sm:gap-2 border-glamspot-neutral-200 text-glamspot-neutral-700 hover:bg-glamspot-neutral-50 text-xs sm:text-sm px-2 sm:px-4"
        onClick={handleFilterClick}
      >
        <SlidersHorizontal className="w-3 h-3 sm:w-4 sm:h-4" />
        <span className="hidden xs:inline">Filters</span>
      </Button>

      <Button
        variant="outline"
        size="sm"
        className={`flex items-center gap-1 sm:gap-2 border-glamspot-neutral-200 text-glamspot-neutral-700 hover:bg-glamspot-neutral-50 text-xs sm:text-sm px-2 sm:px-4 ${
          viewMode === 'map' ? 'bg-glamspot-neutral-100' : ''
        }`}
        onClick={handleMapToggle}
      >
        {viewMode === 'grid' ? (
          <>
            <Map className="w-3 h-3 sm:w-4 sm:h-4" />
            <span className="hidden xs:inline">Map</span>
          </>
        ) : (
          <>
            <Grid3X3 className="w-3 h-3 sm:w-4 sm:h-4" />
            <span className="hidden xs:inline">List</span>
          </>
        )}
      </Button>
    </div>
  );
};